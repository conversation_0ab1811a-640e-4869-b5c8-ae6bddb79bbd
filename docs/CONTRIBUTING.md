# Contribution Guidelines

Welcome, and thank you for your interest in contributing. This project is governed with a clear vision and strong direction. The following guidelines exist to keep contributions productive, focused, and aligned with the project's goals.

1. **Project Governance**

This is not a democracy. The project is maintained by a *benevolent dictator*--that’s me. Final decisions rest with the maintainer. Suggestions are welcome, but proposals that conflict with the core philosophy or goals of the project may be closed without further discussion.

2. **Pull Requests**

- **Feature Suggestions**: Open to ideas, but keep them aligned with the project's direction. Off-mission PRs will be closed.
- **Bug Fixes**: Fixes must include tests that directly verifies the issue described in the report. If the bug can't be demonstrated, it doesn't exist (or at least, it's not our problem).
- **Test Coverage**: PRs that improve test coverage are encouraged. If there's a need to refactor code to enable testing, that's acceptable--but any touched area must remain fully covered. Incomplete refactors or those introducing new test gaps will be rejected.

3. **Code Style & Quality**

- Follow existing code patterns. If you think something should be improved, propose it, but don't go on a "style crusade."
- Consistency and clarity matter more than cleverness, always.

4. **Communication**

- Be respectful by default. We're here to build, not babysit.
- If someone is being willfully obtuse or disruptive, use your judgement. Free speech is respected, but consequences follow unproductive behavior.

5. **Philosophy**

This project prioritizes:

- Balance speed + untility with simplicity vs complexity, bias towards performance
- Practicality over purity.
- Results over ceremony.

Stay focused, keep your contributions sharp, and understand the mission. If that sounds like your style, you're more than welcome here.

P.S. These contribution guidelines were 35 lines long when written the first time. If they're still not 35 lines long, then it's safe to assume that someone has done something that required we clarify things for those with a lower IQ than ours.
